#!/usr/bin/env python3
"""
Bin檔案格式化工具使用範例
"""

import os
import tempfile
from bin_formatter import BinFormatter

def create_sample_bin():
    """創建一個範例bin檔案用於演示"""
    sample_content = b"""
    This is a sample firmware binary file.
    It contains some mock firmware data for demonstration purposes.
    In real usage, this would be actual compiled firmware code.
    """ * 10  # 重複內容讓檔案更大一些
    
    with tempfile.NamedTemporaryFile(suffix='.bin', delete=False) as f:
        f.write(sample_content)
        return f.name

def demonstrate_usage():
    """演示工具的各種使用方式"""
    print("=== Bin檔案格式化工具使用範例 ===\n")
    
    # 創建範例檔案
    sample_bin = create_sample_bin()
    print(f"創建範例bin檔案: {sample_bin}")
    print(f"檔案大小: {os.path.getsize(sample_bin)} bytes\n")
    
    formatter = BinFormatter()
    
    try:
        # 範例1: Gateway WiFi韌體 (GW-001型號)
        print("範例1: 格式化Gateway WiFi韌體")
        output1 = formatter.format_bin_file(
            bin_path=sample_bin,
            device_type="gateway",
            function_type="wifi",
            version="*******",
            model="GW-001",
            min_hw_version="*******",
            max_hw_version="*******"
        )
        print(f"輸出檔案: {os.path.basename(output1)}")
        print(f"檔案大小: {os.path.getsize(output1)} bytes")
        print(f"模型名稱: GW-001\n")

        # 範例2: Gateway BLE韌體 (GW-002型號)
        print("範例2: 格式化Gateway BLE韌體")
        output2 = formatter.format_bin_file(
            bin_path=sample_bin,
            device_type="gateway",
            function_type="ble",
            version="2.1.3.5",
            model="GW-002",
            min_hw_version="1.5.0.0",
            max_hw_version="3.0.0.0"
        )
        print(f"輸出檔案: {os.path.basename(output2)}")
        print(f"檔案大小: {os.path.getsize(output2)} bytes")
        print(f"模型名稱: GW-002\n")

        # 範例3: EPD BLE韌體 (EPD-001型號)
        print("範例3: 格式化EPD BLE韌體")
        output3 = formatter.format_bin_file(
            bin_path=sample_bin,
            device_type="epd",
            function_type="ble",
            version="0.9.1.2",
            model="EPD-001",
            min_hw_version="0.5.0.0",
            max_hw_version="1.5.0.0"
        )
        print(f"輸出檔案: {os.path.basename(output3)}")
        print(f"檔案大小: {os.path.getsize(output3)} bytes")
        print(f"模型名稱: EPD-001\n")

        # 顯示工具信息
        print("工具信息:")
        print(f"支援的設備類型: {', '.join(formatter.get_device_types())}")
        print(f"支援的功能類型: {', '.join(formatter.get_function_types())}")
        print(f"Gateway支援的功能: {', '.join(formatter.get_supported_functions('gateway'))}")
        print(f"EPD支援的功能: {', '.join(formatter.get_supported_functions('epd'))}")

        print(f"\n所有輸出檔案已儲存到: {formatter.output_dir}")

        # 測試解析功能
        print("\n測試解析功能:")
        try:
            parsed1 = formatter.parse_bin_file(output1)
            print(f"範例1解析結果: {parsed1['device_type']} {parsed1['model']} {parsed1['function_type']} v{parsed1['version']}")

            parsed2 = formatter.parse_bin_file(output2)
            print(f"範例2解析結果: {parsed2['device_type']} {parsed2['model']} {parsed2['function_type']} v{parsed2['version']}")

            parsed3 = formatter.parse_bin_file(output3)
            print(f"範例3解析結果: {parsed3['device_type']} {parsed3['model']} {parsed3['function_type']} v{parsed3['version']}")
        except Exception as e:
            print(f"解析測試失敗: {e}")
        
    except Exception as e:
        print(f"錯誤: {e}")
    
    finally:
        # 清理範例檔案
        if os.path.exists(sample_bin):
            os.unlink(sample_bin)

def demonstrate_error_handling():
    """演示錯誤處理"""
    print("\n=== 錯誤處理演示 ===\n")
    
    formatter = BinFormatter()
    sample_bin = create_sample_bin()
    
    error_cases = [
        # (描述, 參數)
        ("不存在的檔案", ("nonexistent.bin", "gateway", "wifi", "*******", 0, "*******", "*******")),
        ("不支援的設備類型", (sample_bin, "invalid_device", "wifi", "*******", 0, "*******", "*******")),
        ("不支援的功能類型", (sample_bin, "gateway", "invalid_function", "*******", 0, "*******", "*******")),
        ("設備功能不匹配", (sample_bin, "epd", "wifi", "*******", 0, "*******", "*******")),
        ("無效版本格式", (sample_bin, "gateway", "wifi", "1.0.0", 0, "*******", "*******")),
        ("無效裝置編號", (sample_bin, "gateway", "wifi", "*******", 999, "*******", "*******")),
        ("無效硬體版本格式", (sample_bin, "gateway", "wifi", "*******", 0, "invalid", "*******")),
    ]
    
    for description, args in error_cases:
        try:
            formatter.format_bin_file(*args)
            print(f"❌ {description}: 應該要失敗但沒有")
        except Exception as e:
            print(f"✅ {description}: {e}")
    
    # 清理
    if os.path.exists(sample_bin):
        os.unlink(sample_bin)

if __name__ == "__main__":
    demonstrate_usage()
    demonstrate_error_handling()
    
    print("\n=== 演示完成 ===")
    print("您可以檢查 output/ 目錄中的生成檔案")
    print("使用 python bin_formatter.py 來互動式使用工具")
